# Contributing to LLM Memory Test Framework

Thank you for your interest in contributing to the LLM Memory Test Framework! This document provides guidelines and information for contributors.

## Code of Conduct

This project and everyone participating in it is governed by our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates. When creating a bug report, please include:

- A clear and descriptive title
- Steps to reproduce the issue
- Expected vs actual behavior
- Your environment details (OS, Node.js version, etc.)
- Configuration details (without sensitive information)

### Suggesting Features

Feature suggestions are welcome! Please:

- Use a clear and descriptive title
- Provide a detailed description of the suggested feature
- Explain why this feature would be useful
- Consider providing examples or mockups

### Contributing Code

#### Development Setup

1. Fork the repository
2. Clone your fork: `git clone https://github.com/your-username/llm-memory-test-framework.git`
3. Install dependencies: `npm install`
4. Create a branch: `git checkout -b feature/your-feature-name`

#### Development Guidelines

- Follow the existing code style and conventions
- Write comprehensive tests for new functionality
- Add JSDoc comments for all public methods and classes
- Ensure all tests pass: `npm test`
- Run linting: `npm run lint`
- Format code: `npm run format`

#### Memory Implementation Guidelines

When contributing new memory implementations:

1. Extend the `MemoryInterface` class
2. Follow the interface contract exactly
3. Include comprehensive unit tests
4. Add integration tests
5. Document the implementation in `MEMORY_IMPLEMENTATION_GUIDE.md`
6. Include performance benchmarks
7. Add configuration schema validation

#### Testing

- Write unit tests for all new functionality
- Include integration tests for complex features
- Ensure test coverage remains high
- Test with different LLM providers when applicable

#### Documentation

- Update relevant documentation files
- Add JSDoc comments for new code
- Include usage examples where appropriate
- Update the README if needed

### Pull Request Process

1. Ensure your code follows the project standards
2. Update documentation as needed
3. Add or update tests as appropriate
4. Ensure all tests pass
5. Create a pull request with a clear title and description
6. Link any related issues
7. Be responsive to feedback during review

## Development Scripts

- `npm start` - Run the application
- `npm run dev` - Run with file watching
- `npm test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## Project Structure

```
src/
├── config/          # Configuration management
├── memory/          # Memory implementations
├── models/          # LLM model interfaces
├── utils/           # Utility functions
├── validation/      # Data validation schemas
└── __tests__/       # Test files
```

## Coding Standards

- Use ES6+ features appropriately
- Follow the existing naming conventions
- Keep functions focused and small
- Use meaningful variable and function names
- Add error handling for all external calls
- Use the centralized logging system

## Questions?

If you have questions about contributing, please:

1. Check the existing documentation
2. Search existing issues
3. Create a new issue with the "question" label

Thank you for contributing to the LLM Memory Test Framework!
