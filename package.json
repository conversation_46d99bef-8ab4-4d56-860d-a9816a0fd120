{"name": "llm-memory-test-framework", "version": "1.0.0", "description": "A comprehensive framework for testing and evaluating different Large Language Model (LLM) memory implementations through simulated conversations and systematic evaluation", "main": "src/app.js", "bin": {"llm-memory-test": "./src/app.js"}, "files": ["src/", "data/", "README.md", "LICENSE", "ARCHITECTURE.md", "CONFIGURATION.md", "EVALUATION_FRAMEWORK_GUIDE.md", "MEMORY_IMPLEMENTATION_GUIDE.md", "USAGE_EXAMPLES.md"], "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "lint": "eslint src/ --ext .js || echo 'No files to lint'", "lint:fix": "eslint src/ --ext .js --fix || echo 'No files to lint'", "format": "prettier --write \"src/**/*.js\" \"*.{js,json,md}\"", "format:check": "prettier --check \"src/**/*.js\" \"*.{js,json,md}\"", "precommit": "npm run lint && npm run format:check && npm run test", "prepare": "npm run lint && npm run test", "prepublishOnly": "npm run test && npm run lint", "postinstall": "node scripts/verify-installation.js", "health-check": "node scripts/health-check.js", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags"}, "keywords": ["llm", "large-language-model", "memory", "memory-testing", "conversation-simulation", "ai-testing", "chatbot-testing", "memory-evaluation", "conversation-ai", "llm-framework", "ai-memory", "conversational-ai", "memory-retention", "ai-evaluation", "testing-framework", "openai", "openrouter", "langchain"], "author": "LLM Memory Test Contributors", "license": "MIT", "homepage": "https://github.com/your-username/llm-memory-test-framework#readme", "repository": {"type": "git", "url": "git+https://github.com/your-username/llm-memory-test-framework.git"}, "bugs": {"url": "https://github.com/your-username/llm-memory-test-framework/issues"}, "dependencies": {"@langchain/openai": "^0.5.5", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "dotenv": "^16.4.7", "langchain": "^0.3.21", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0"}}