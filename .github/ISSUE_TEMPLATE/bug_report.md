---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. iOS]
 - Node.js version: [e.g. 18.0.0]
 - Package version: [e.g. 1.0.0]
 - LLM Provider: [e.g. OpenRouter, OpenAI]

**Configuration**
Please share your configuration (remove any sensitive information like API keys):
```json
{
  "memory_type": "...",
  "model": "...",
  // other relevant config
}
```

**Additional context**
Add any other context about the problem here.
