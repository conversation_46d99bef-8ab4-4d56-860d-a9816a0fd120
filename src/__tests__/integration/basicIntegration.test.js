/**
 * Basic Integration Tests
 * 
 * This test suite validates basic integration between core components
 * including memory factory, configuration management, and basic functionality.
 */

const MemoryFactory = require('../../memory/memoryFactory');
const { ConfigManager } = require('../../config');

// Mock external dependencies
jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    })),
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('Basic Integration Tests', () => {
  let originalEnv;

  beforeAll(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.OPENROUTER_API_KEY = 'test-key-integration';
  });

  afterAll(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Configuration Management Integration', () => {
    test('should create and validate configuration', () => {
      const config = new ConfigManager();
      expect(config).toBeDefined();
      
      // Test basic configuration access
      const memoryConfig = config.get('memory');
      expect(memoryConfig).toBeDefined();
      expect(memoryConfig.type).toBe('simple');
    });

    test('should handle environment variable overrides', () => {
      process.env.MEMORY_TYPE = 'summary';
      const config = new ConfigManager();
      
      const memoryType = config.get('memory.type');
      expect(memoryType).toBe('summary');
      
      // Clean up
      delete process.env.MEMORY_TYPE;
    });
  });

  describe('Memory Factory Integration', () => {
    test('should create simple memory instance', () => {
      const sessionId = 'test-session-123';
      const memory = MemoryFactory.createMemory('simple', sessionId);
      
      expect(memory).toBeDefined();
      expect(memory.sessionId).toBe(sessionId);
    });

    test('should create summary memory instance', () => {
      const sessionId = 'test-session-456';
      const memory = MemoryFactory.createMemory('summary', sessionId);
      
      expect(memory).toBeDefined();
      expect(memory.sessionId).toBe(sessionId);
    });

    test('should create memory with custom configuration', () => {
      const config = new ConfigManager();
      const sessionId = 'test-session-789';
      const memory = MemoryFactory.createMemory('simple', sessionId, config);
      
      expect(memory).toBeDefined();
      expect(memory.sessionId).toBe(sessionId);
    });

    test('should get supported memory types', () => {
      const types = MemoryFactory.getSupportedTypes();
      expect(types).toContain('simple');
      expect(types).toContain('summary');
      expect(types).toContain('summary_with_knowledge');
    });
  });

  describe('Memory Operations Integration', () => {
    test('should add and retrieve messages from simple memory', async () => {
      const memory = MemoryFactory.createMemory('simple', 'test-session');

      const message = { role: 'user', content: 'Hello, world!' };
      await memory.addMessage(message);

      // Access the messages array directly
      expect(memory.messages).toHaveLength(1);
      expect(memory.messages[0].role).toBe(message.role);
      expect(memory.messages[0].content).toBe(message.content);
    });

    test('should handle memory context window limits', async () => {
      // Set environment variable to configure context window
      process.env.MEMORY_CONTEXT_WINDOW = '2';
      const config = new ConfigManager();

      const memory = MemoryFactory.createMemory('simple', 'test-session', config);

      // Add more messages than context window
      await memory.addMessage({ role: 'user', content: 'Message 1' });
      await memory.addMessage({ role: 'assistant', content: 'Response 1' });
      await memory.addMessage({ role: 'user', content: 'Message 2' });

      // Access the messages array directly
      expect(memory.messages).toHaveLength(2);
      expect(memory.messages[0].content).toBe('Response 1');
      expect(memory.messages[1].content).toBe('Message 2');

      // Clean up
      delete process.env.MEMORY_CONTEXT_WINDOW;
    });
  });

  describe('Configuration Validation Integration', () => {
    test('should validate complete configuration', () => {
      const config = new ConfigManager();
      const validation = config.validateConfigurationWithFeedback();
      
      expect(validation).toBeDefined();
      expect(validation.isValid).toBe(true);
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });

    test('should detect invalid memory configuration', () => {
      // Set invalid environment variable
      process.env.MEMORY_CONTEXT_WINDOW = '-1';

      expect(() => {
        new ConfigManager();
      }).toThrow();

      // Clean up
      delete process.env.MEMORY_CONTEXT_WINDOW;
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle invalid memory type gracefully', () => {
      // MemoryFactory falls back to 'simple' for invalid types, doesn't throw
      const memory = MemoryFactory.createMemory('invalid-type', 'test-session');
      expect(memory).toBeDefined();
      expect(memory.sessionId).toBe('test-session');
    });

    test('should handle missing session ID', () => {
      expect(() => {
        MemoryFactory.createMemory('simple', '');
      }).toThrow();
    });

    test('should handle null session ID', () => {
      expect(() => {
        MemoryFactory.createMemory('simple', null);
      }).toThrow();
    });
  });
});
