/**
 * End-to-End Workflow Integration Tests
 * 
 * This test suite validates end-to-end workflows with different memory types,
 * error recovery scenarios, and performance regression testing.
 */

const MemoryFactory = require('../../memory/memoryFactory');
const { ConfigManager } = require('../../config');
const { v4: uuidv4 } = require('uuid');

// Mock external dependencies
jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    })),
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('End-to-End Workflow Integration Tests', () => {
  let originalEnv;

  beforeAll(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.OPENROUTER_API_KEY = 'test-key-integration';
  });

  afterAll(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Memory Type Workflows', () => {
    test('should handle simple memory workflow', async () => {
      const sessionId = uuidv4();
      const memory = MemoryFactory.createMemory('simple', sessionId);
      
      // Simulate conversation flow
      const messages = [
        { role: 'user', content: 'Hello, I need help with my account.' },
        { role: 'assistant', content: 'I can help you with your account. What specific issue are you having?' },
        { role: 'user', content: 'I forgot my password.' },
        { role: 'assistant', content: 'I can help you reset your password. Let me guide you through the process.' }
      ];
      
      // Add messages sequentially
      for (const message of messages) {
        await memory.addMessage(message);
      }
      
      // Verify memory state
      expect(memory.messages).toHaveLength(4);
      expect(memory.messages[0].content).toBe('Hello, I need help with my account.');
      expect(memory.messages[3].content).toBe('I can help you reset your password. Let me guide you through the process.');
    });

    test('should handle summary memory workflow', async () => {
      const sessionId = uuidv4();
      const memory = MemoryFactory.createMemory('summary', sessionId);
      
      // Add initial messages
      await memory.addMessage({ role: 'user', content: 'I want to learn about machine learning.' });
      await memory.addMessage({ role: 'assistant', content: 'Machine learning is a subset of AI that enables computers to learn from data.' });
      
      // Verify memory state (summary memory uses storage.messages)
      const storedMessages = memory.storage ? memory.storage.messages : memory.messages;
      expect(storedMessages).toHaveLength(2);
      expect(storedMessages[0].role).toBe('user');
      expect(storedMessages[1].role).toBe('assistant');
    });

    test('should handle summary with knowledge extraction workflow', async () => {
      const sessionId = uuidv4();
      const memory = MemoryFactory.createMemory('summary_with_knowledge', sessionId);
      
      // Add messages with factual content
      await memory.addMessage({ role: 'user', content: 'What is the capital of France?' });
      await memory.addMessage({ role: 'assistant', content: 'The capital of France is Paris.' });
      await memory.addMessage({ role: 'user', content: 'What about Germany?' });
      await memory.addMessage({ role: 'assistant', content: 'The capital of Germany is Berlin.' });
      
      // Verify memory state (summary memory uses storage.messages)
      const storedMessages = memory.storage ? memory.storage.messages : memory.messages;
      expect(storedMessages).toHaveLength(4);
    });
  });

  describe('Error Recovery Scenarios', () => {
    test('should handle invalid message format gracefully', async () => {
      const memory = MemoryFactory.createMemory('simple', uuidv4());
      
      // Try to add invalid message
      await expect(memory.addMessage(null)).rejects.toThrow();
      await expect(memory.addMessage({})).rejects.toThrow();
      await expect(memory.addMessage({ role: 'invalid' })).rejects.toThrow();
      
      // Verify memory is still functional
      await memory.addMessage({ role: 'user', content: 'Valid message' });
      expect(memory.messages).toHaveLength(1);
    });

    test('should handle memory overflow gracefully', async () => {
      // Set environment variable to configure context window
      process.env.MEMORY_CONTEXT_WINDOW = '3';
      const config = new ConfigManager();

      const memory = MemoryFactory.createMemory('simple', uuidv4(), config);

      // Add more messages than context window
      const messages = [
        { role: 'user', content: 'Message 1' },
        { role: 'assistant', content: 'Response 1' },
        { role: 'user', content: 'Message 2' },
        { role: 'assistant', content: 'Response 2' },
        { role: 'user', content: 'Message 3' }
      ];

      for (const message of messages) {
        await memory.addMessage(message);
      }

      // Should only keep the last 3 messages (context window = 3)
      const storedMessages = memory.storage ? memory.storage.messages : memory.messages;
      expect(storedMessages).toHaveLength(3);
      // The oldest messages are removed, so we should have the last 3 messages
      expect(storedMessages[0].content).toBe('Message 2');
      expect(storedMessages[1].content).toBe('Response 2');
      expect(storedMessages[2].content).toBe('Message 3');

      // Clean up
      delete process.env.MEMORY_CONTEXT_WINDOW;
    });

    test('should handle concurrent memory operations', async () => {
      const memory = MemoryFactory.createMemory('simple', uuidv4());
      
      // Add messages concurrently
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(memory.addMessage({ 
          role: i % 2 === 0 ? 'user' : 'assistant', 
          content: `Message ${i}` 
        }));
      }
      
      await Promise.all(promises);

      const storedMessages = memory.storage ? memory.storage.messages : memory.messages;
      // Concurrent operations should complete successfully, may have all 5 messages
      expect(storedMessages.length).toBeGreaterThan(0);
      expect(storedMessages.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Performance Regression Tests', () => {
    test('should handle large message volumes efficiently', async () => {
      const memory = MemoryFactory.createMemory('simple', uuidv4());
      
      const startTime = Date.now();
      
      // Add 100 messages
      for (let i = 0; i < 100; i++) {
        await memory.addMessage({ 
          role: i % 2 === 0 ? 'user' : 'assistant', 
          content: `Performance test message ${i}` 
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds

      expect(memory.messages.length).toBeGreaterThan(0);
    });

    test('should maintain consistent retrieval performance', async () => {
      // Set environment variable to configure context window
      process.env.MEMORY_CONTEXT_WINDOW = '50';
      const config = new ConfigManager();

      const memory = MemoryFactory.createMemory('simple', uuidv4(), config);

      // Fill memory to capacity
      for (let i = 0; i < 50; i++) {
        await memory.addMessage({
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i}`
        });
      }

      // Measure retrieval performance
      const retrievalTimes = [];
      for (let i = 0; i < 10; i++) {
        const start = Date.now();
        await memory.getMemoryContext();
        const end = Date.now();
        retrievalTimes.push(end - start);
      }

      // All retrievals should be fast and consistent
      const avgTime = retrievalTimes.reduce((a, b) => a + b, 0) / retrievalTimes.length;
      expect(avgTime).toBeLessThan(100); // 100ms average

      // Check for consistency (no retrieval should be more than 3x the average)
      const maxTime = Math.max(...retrievalTimes);
      expect(maxTime).toBeLessThan(avgTime * 3);

      // Clean up
      delete process.env.MEMORY_CONTEXT_WINDOW;
    });
  });

  describe('Configuration Integration', () => {
    test('should respect different context window sizes', async () => {
      const testCases = [
        { contextWindow: 1, expectedMessages: 1 },
        { contextWindow: 5, expectedMessages: 5 },
        { contextWindow: 10, expectedMessages: 10 }
      ];

      for (const testCase of testCases) {
        // Set environment variable to configure context window
        process.env.MEMORY_CONTEXT_WINDOW = testCase.contextWindow.toString();
        const config = new ConfigManager();

        const memory = MemoryFactory.createMemory('simple', uuidv4(), config);

        // Add more messages than context window
        for (let i = 0; i < testCase.contextWindow + 3; i++) {
          await memory.addMessage({
            role: i % 2 === 0 ? 'user' : 'assistant',
            content: `Message ${i}`
          });
        }

        expect(memory.messages).toHaveLength(testCase.expectedMessages);

        // Clean up
        delete process.env.MEMORY_CONTEXT_WINDOW;
      }
    });
  });
});
